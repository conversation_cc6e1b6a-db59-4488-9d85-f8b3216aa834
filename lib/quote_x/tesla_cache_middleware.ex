defmodule QuoteX.TeslaCacheMiddleware do
  @behaviour Tesla.Middleware

  @ttl Application.compile_env(:quotex, [:tesla_cache, :ttl], 60)
  @cache_name Application.compile_env(:quotex, [:tesla_cache, :name], :tesla_cache)

  @impl true
  def call(env, next, _opts) do
    key = cache_key(env)

    case Cachex.get(@cache_name, key) do
      {:ok, nil} ->
        with {:ok, env} <- Tesla.run(env, next) do
          ttl = ttl_for(env)
          # Only cache successful GETs
          if env.method in [:get, :head] and env.status in 200..299 do
            _ = Cachex.put(@cache_name, key, env, ttl: ttl)
          end
          {:ok, env}
        end

      {:ok, %Tesla.Env{} = cached} ->
        {:ok, cached}

      {:error, _} ->
        Tesla.run(env, next)
    end
  end

  defp ttl_for(env) do
    case get_resp_cache_control_max_age(env) do
      nil -> @ttl
      seconds -> seconds
    end
  end

  defp get_resp_cache_control_max_age(%Tesla.Env{headers: headers}) do
    with {_, value} <- Enum.find(headers, fn {k, _} -> String.downcase(k) == "cache-control" end),
         [_, max_age] <- Regex.run(~r/max-age=(\d+)/, value) do
      String.to_integer(max_age)
    else
      _ -> nil
    end
  end

  defp cache_key(%Tesla.Env{method: method, url: url, query: query, headers: headers}) do
    normalized_headers =
      headers
      |> Enum.map(fn {k, v} -> {String.downcase(k), v} end)
      |> Enum.filter(fn {k, _} -> k in ["accept", "content-type", "authorization"] end)

    :erlang.phash2({method, url, query || %{}, normalized_headers})
  end
end
