defmodule QuoteX.Accounts do
  @moduledoc """
  The Accounts domain is responsible for managing operators and properties.
  """
  use Ash.Domain,
    otp_app: :quotex,
    extensions: [AshAdmin.Domain, AshPaperTrail.Domain, AshGraphql.Domain]

  graphql do
    queries do
      get QuoteX.Accounts.Property, :get_ticket, :read
      list QuoteX.Accounts.Property, :list_tickets, :read
      get QuoteX.Accounts.Reservation, :get_reservation, :read
      list QuoteX.Accounts.Reservation, :list_reservations, :read, paginate_with: nil
    end
  end

  paper_trail do
    include_versions? true
  end

  admin do
    show? true
  end

  resources do
    resource QuoteX.Accounts.Property
    resource QuoteX.Accounts.Property.Version
    resource QuoteX.Accounts.Reservation
  end
end
